<?php

namespace api\data_table\column_preferences;

use function api\data_table\data_table_filter;
use edge\edge;
use data_table\data_table;
use system\data_table_storage;
/**
 * Get current column preferences from database
 */
function get_column_preferences($table_name){
    $user_id = data_table_storage::get_current_user_id();
    $config = data_table_storage::get_configuration($table_name, $user_id);

    if ($config) {
        return $config['configuration'];
    }

    return ['hidden' => [], 'structure' => []];
}

/**
 * Save column preferences to database
 */
function save_column_preferences($table_name, $preferences, $data_source = null){
    $user_id = data_table_storage::get_current_user_id();

    // Convert data_source to proper type (int or null)
    if ($data_source === '' || $data_source === '0' || $data_source === 0) {
        $data_source = null;
    } elseif (is_string($data_source) && is_numeric($data_source)) {
        $data_source = (int)$data_source;
    }

    return data_table_storage::save_configuration($table_name, $preferences, $user_id, $data_source);
}

/**
 * Initialize column structure if it doesn't exist
 */
function initialize_column_structure($table_name, $original_columns) {
    $preferences = get_column_preferences($table_name);

    // If structure doesn't exist, create it from original columns
    if (empty($preferences['structure'])) {
        $structure = [];
        $hidden = $preferences['hidden'] ?? [];

        foreach ($original_columns as $index => $col) {
            $column_id = 'col_' . $index . '_' . md5($col['label']);
            $structure[] = [
                'id' => $column_id,
                'label' => $col['label'],
                'fields' => is_array($col['field']) ? $col['field'] : [$col['field']],
                'filter' => $col['filter'] ?? false,
                'visible' => !in_array($column_id, $hidden)
            ];
        }

        $preferences['structure'] = $structure;
        save_column_preferences($table_name, $preferences);
    }

    return $preferences;
}

// regenerate_table function removed - functionality moved to data_table class


function save($p) {
    $table_name = $p['table_name'] ?? '';

    $preferences = get_column_preferences($table_name);
    // Original save functionality
    $hidden_columns = json_decode($p['hidden_columns'] ?? '[]', true);
    $column_structure = json_decode($p['column_structure'] ?? '[]', true);

    $updated_preferences = array_merge($preferences, [
        'hidden' => $hidden_columns,
        'structure' => $column_structure,
        'updated_at' => date('Y-m-d H:i:s')
    ]);

    save_column_preferences($table_name, $updated_preferences, $p['data_source'] ?? null);
    return data_table::regenerate_with_preferences($p);
}

function toggle_column($p) {
    $table_name = $p['table_name'] ?? '';

    $preferences = get_column_preferences($table_name);
    $column_id = $p['column_id'] ?? '';
    if (empty($column_id)) {
        throw new Exception('Column ID is required');
    }

    $hidden = $preferences['hidden'] ?? [];
    $structure = $preferences['structure'] ?? [];
    $index = array_search($column_id, $hidden);

    if ($index !== false) {
        // Column is hidden, show it
        unset($hidden[$index]);
        $hidden = array_values($hidden); // Re-index array
        $is_visible = true;
    } else {
        // Column is visible, hide it
        $hidden[] = $column_id;
        $is_visible = false;
    }

    // Update the structure array to sync visible property
    foreach ($structure as &$column) {
        if ($column['id'] === $column_id) {
            $column['visible'] = $is_visible;
            break;
        }
    }

    $updated_preferences = array_merge($preferences, [
        'hidden' => $hidden,
        'structure' => $structure,
        'updated_at' => date('Y-m-d H:i:s')
    ]);

    // Debug removed for production

    save_column_preferences($table_name, $updated_preferences, $p['data_source'] ?? null);

    return data_table::regenerate_with_preferences($p);
}

function show_all_columns($p) {
    $table_name = $p['table_name'] ?? '';
    $preferences = get_column_preferences($table_name);
    $structure = $preferences['structure'] ?? [];

    // Update all columns in structure to be visible
    foreach ($structure as &$column) {
        $column['visible'] = true;
    }

    $updated_preferences = array_merge($preferences, [
        'hidden' => [],
        'structure' => $structure,
        'updated_at' => date('Y-m-d H:i:s')
    ]);

    save_column_preferences($table_name, $updated_preferences, $p['data_source'] ?? null);
    return data_table::regenerate_with_preferences($p);
}

function hide_all_columns($p) {
    $table_name = $p['table_name'] ?? '';

    $preferences = get_column_preferences($table_name);
    // Get all column IDs from structure or generate from current columns
    $structure = $preferences['structure'] ?? [];
    $all_column_ids = [];

    if (!empty($structure)) {
        $all_column_ids = array_column($structure, 'id');

        // Update all columns in structure to be hidden
        foreach ($structure as &$column) {
            $column['visible'] = false;
        }
    } else {
        // If no structure, we can't hide all columns
        throw new Exception('No column structure available');
    }

    $updated_preferences = array_merge($preferences, [
        'hidden' => $all_column_ids,
        'structure' => $structure,
        'updated_at' => date('Y-m-d H:i:s')
    ]);

    save_column_preferences($table_name, $updated_preferences, $p['data_source'] ?? null);
    return data_table::regenerate_with_preferences($p);
}

function add_column($p) {
    $table_name = $p['table_name'] ?? '';

    $preferences = get_column_preferences($table_name);
    $column_name = trim($p['column_name'] ?? '');
    if (empty($column_name)) {
        throw new Exception('Column name is required');
    }

    $structure = $preferences['structure'] ?? [];
    $new_id = "col_new_" . time();
    $new_column = [
        'id' => $new_id,
        'label' => $column_name,
        'fields' => [],
        'filter' => false,
        'visible' => true
    ];

    $structure[] = $new_column;
    $preferences['structure'] = $structure;
    save_column_preferences($table_name, $preferences);
    return data_table::regenerate_with_preferences($p);
}

function remove_column($p) {
    $table_name = $p['table_name'] ?? '';

    $preferences = get_column_preferences($table_name);
    $column_id = $p['column_id'] ?? '';
    if (empty($column_id)) {
        throw new Exception('Column ID is required');
    }

    $structure = $preferences['structure'] ?? [];
    $structure = array_filter($structure, function ($col) use ($column_id) {
        return $col['id'] !== $column_id;
    });
    $structure = array_values($structure); // Re-index array

    // Also remove from hidden columns if present
    $hidden = $preferences['hidden'] ?? [];
    $hidden = array_filter($hidden, function ($id) use ($column_id) {
        return $id !== $column_id;
    });
    $hidden = array_values($hidden);

    $preferences['structure'] = $structure;
    $preferences['hidden'] = $hidden;
    save_column_preferences($table_name, $preferences);
    return data_table::regenerate_with_preferences($p);
}

function add_field_to_column($p) {
    $table_name = $p['table_name'] ?? '';
    $preferences = get_column_preferences($table_name);
    $field_name = $p['field_name'] ?? '';
    $column_id = $p['column_id'] ?? '';

    if (empty($field_name) || empty($column_id)) {
        throw new Exception('Field name and column ID are required');
    }

    $structure = $preferences['structure'] ?? [];
    $hidden = $preferences['hidden'] ?? [];

    foreach ($structure as &$column) {
        if ($column['id'] === $column_id) {
            if (!in_array($field_name, $column['fields'])) {
                $column['fields'][] = $field_name;
            }
            // Ensure visible property is set
            $column['visible'] = !in_array($column['id'], $hidden);
            break;
        }
    }
    $preferences['structure'] = $structure;
    save_column_preferences($table_name, $preferences);
    return data_table::regenerate_with_preferences($p);
}

function remove_field_from_column($p) {
    $table_name = $p['table_name'] ?? '';

    $preferences = get_column_preferences($table_name);
    $field_name = $p['field_name'] ?? '';
    $column_id = $p['column_id'] ?? '';

    if (empty($field_name) || empty($column_id)) {
        throw new Exception('Field name and column ID are required');
    }

    $structure = $preferences['structure'] ?? [];
    $hidden = $preferences['hidden'] ?? [];

    foreach ($structure as &$column) {
        if ($column['id'] === $column_id) {
            $column['fields'] = array_filter($column['fields'], function ($field) use ($field_name) {
                return $field !== $field_name;
            });
            $column['fields'] = array_values($column['fields']); // Re-index
            // Ensure visible property is set
            $column['visible'] = !in_array($column['id'], $hidden);
            break;
        }
    }
    $preferences['structure'] = $structure;
    save_column_preferences($table_name, $preferences);
    return data_table::regenerate_with_preferences($p);
}

function move_field($p) {
    $table_name = $p['table_name'] ?? '';

    $preferences = get_column_preferences($table_name);
    $field_name = $p['field_name'] ?? '';
    $source_column_id = $p['source_column_id'] ?? '';
    $target_column_id = $p['target_column_id'] ?? '';

    if (empty($field_name) || empty($target_column_id)) {
        throw new Exception('Field name and target column ID are required');
    }

    $structure = $preferences['structure'] ?? [];
    $hidden = $preferences['hidden'] ?? [];

    // Remove from source column if specified
    if (!empty($source_column_id)) {
        foreach ($structure as &$column) {
            if ($column['id'] === $source_column_id) {
                $column['fields'] = array_filter($column['fields'], function ($field) use ($field_name) {
                    return $field !== $field_name;
                });
                $column['fields'] = array_values($column['fields']);
                // Ensure visible property is set
                $column['visible'] = !in_array($column['id'], $hidden);
                break;
            }
        }
    }

    // Add to target column
    foreach ($structure as &$column) {
        if ($column['id'] === $target_column_id) {
            if (!in_array($field_name, $column['fields'])) {
                $column['fields'][] = $field_name;
            }
            // Ensure visible property is set
            $column['visible'] = !in_array($column['id'], $hidden);
            break;
        }
    }

    $preferences['structure'] = $structure;
    save_column_preferences($table_name, $preferences);
    return data_table::regenerate_with_preferences($p);
}

/**
 * Simplified move_field function for new sortable system
 * Handles reordering fields within a column using the new sortable format
 */
function move_field_simple($p) {
    $table_name = $p['table_name'] ?? '';
    $target_column_id = $p['target_column_id'] ?? '';
    $field_names = $p['field_names'] ?? [];
    $action_ids = $p['action_ids'] ?? [];

    if (empty($table_name) || empty($target_column_id)) {
        throw new Exception('Table name and target column ID are required');
    }

    $preferences = get_column_preferences($table_name);
    $structure = $preferences['structure'] ?? [];
    $hidden = $preferences['hidden'] ?? [];

    // Find the target column and update its fields and actions
    foreach ($structure as &$column) {
        if ($column['id'] === $target_column_id) {
            // Update fields in the new order
            $column['fields'] = array_values($field_names);

            // Update action buttons if provided
            if (!empty($action_ids)) {
                // Get existing action buttons and reorder them
                $existing_actions = $column['action_buttons'] ?? [];
                $reordered_actions = [];

                foreach ($action_ids as $action_id) {
                    foreach ($existing_actions as $action) {
                        if ($action['id'] === $action_id) {
                            $reordered_actions[] = $action;
                            break;
                        }
                    }
                }

                $column['action_buttons'] = $reordered_actions;
            }

            // Ensure visible property is set
            $column['visible'] = !in_array($column['id'], $hidden);
            break;
        }
    }

    $preferences['structure'] = $structure;
    save_column_preferences($table_name, $preferences, $p['data_source'] ?? null);
    return data_table::regenerate_with_preferences($p);
}

function reorder_columns($p) {
    $table_name = $p['table_name'] ?? '';

    // Handle multiple formats for column order
    if (isset($p['column_order']) && is_string($p['column_order'])) {
        // JSON string format (from manual HTMX calls)
        $column_order = json_decode($p['column_order'], true);
    } elseif (isset($p['column_order']) && is_array($p['column_order'])) {
        // Array format (from form submission)
        $column_order = $p['column_order'];
    } elseif (isset($p['column_ids']) && is_array($p['column_ids'])) {
        // New simplified sortable format
        $column_order = $p['column_ids'];
    } else {
        $column_order = [];
    }

    if (empty($column_order)) {
        throw new Exception('Column order is required');
    }

    $preferences = get_column_preferences($table_name);
    $structure = $preferences['structure'] ?? [];
    $hidden = $preferences['hidden'] ?? [];



    // If structure is empty, we need to initialize it from the actual data
    $just_initialized = false;
    if (empty($structure)) {
        // Get actual data to determine the real field names
        try {
            // First, try to get some sample data to see what fields actually exist
            $data_source_id = $p['data_source'] ?? null;
            if ($data_source_id) {
                $sample_data = \system\data_source_manager::get_data_source_data($data_source_id, ['limit' => 1]);
                $sample_items = $sample_data['data'] ?? [];

                if (!empty($sample_items)) {
                    $structure = [];
                    $column_index = 0;
                    $first_item = reset($sample_items);

                    foreach (array_keys($first_item) as $field_name) {
                        // Skip system columns
                        if (in_array($field_name, ['id', 'data_hash', 'created_at', 'updated_at'])) {
                            continue;
                        }

                        $column_id = 'col_' . $column_index . '_' . md5($field_name);
                        $structure[] = [
                            'id' => $column_id,
                            'label' => ucwords(str_replace('_', ' ', $field_name)),
                            'field' => $field_name,
                            'fields' => [$field_name],
                            'filter' => true,
                            'visible' => true
                        ];
                        $column_index++;
                    }

                    $preferences['structure'] = $structure;
                    $just_initialized = true;
                }
            }
        } catch (Exception $e) {
            // If we can't get table info, create minimal structure to prevent errors
            $structure = [];
            foreach ($column_order as $index => $column_id) {
                $structure[] = [
                    'id' => $column_id,
                    'visible' => true,
                    'fields' => [],
                    'label' => 'Column ' . ($index + 1)
                ];
            }
            $preferences['structure'] = $structure;
            $just_initialized = true;
        }
    }

    // Only reorder if we didn't just initialize (since IDs won't match)
    if (!$just_initialized) {
        // Create a lookup array for existing columns
        $columns_by_id = [];
        foreach ($structure as $column) {
            $columns_by_id[$column['id']] = $column;
        }

        // Reorder structure based on the provided order
        $reordered_structure = [];
        foreach ($column_order as $column_id) {
            if (isset($columns_by_id[$column_id])) {
                $column = $columns_by_id[$column_id];
                // Ensure visible property is set correctly
                $column['visible'] = !in_array($column_id, $hidden);
                $reordered_structure[] = $column;
            }
        }

        $preferences['structure'] = $reordered_structure;
    }

    save_column_preferences($table_name, $preferences, $p['data_source'] ?? null);
    return data_table::regenerate_with_preferences($p);
}

/**
 * Rename a column
 */
function rename_column($p) {
    $table_name = $p['table_name'] ?? '';
    $column_id = $p['column_id'] ?? '';
    $new_label = trim($p['new_label'] ?? '');

    if (empty($column_id)) {
        throw new Exception('Column ID is required');
    }

    if (empty($new_label)) {
        throw new Exception('New column name is required');
    }

    $preferences = get_column_preferences($table_name);
    $structure = $preferences['structure'] ?? [];

    // Find and update the column in the structure
    $column_found = false;
    foreach ($structure as &$column) {
        if ($column['id'] === $column_id) {
            $column['label'] = $new_label;
            $column_found = true;
            break;
        }
    }

    if (!$column_found) {
        throw new Exception('Column not found');
    }

    $updated_preferences = array_merge($preferences, [
        'structure' => $structure,
        'updated_at' => date('Y-m-d H:i:s')
    ]);

    save_column_preferences($table_name, $updated_preferences, $p['data_source'] ?? null);

    return data_table::regenerate_with_preferences($p);
}

/**
 * Add action button to a column
 */
function add_action_button($p) {
    $table_name = $p['table_name'] ?? '';
    $column_id = $p['column_id'] ?? '';
    $template = $p['template'] ?? '';
    $field = $p['field'] ?? '';
    $icon = $p['icon'] ?? '';

    if (empty($column_id)) {
        throw new Exception('Column ID is required');
    }

    if (empty($template)) {
        throw new Exception('Template is required');
    }

    if (empty($field)) {
        throw new Exception('Field is required');
    }

    if (empty($icon)) {
        throw new Exception('Icon is required');
    }

    $preferences = get_column_preferences($table_name);
    $structure = $preferences['structure'] ?? [];

    // Find the column and add the action button
    $column_found = false;
    foreach ($structure as &$column) {
        if ($column['id'] === $column_id) {
            if (!isset($column['action_buttons'])) {
                $column['action_buttons'] = [];
            }

            $action_button = [
                'id' => 'action_' . uniqid(),
                'template' => $template,
                'field' => $field,
                'icon' => $icon,
                'created_at' => date('Y-m-d H:i:s')
            ];

            $column['action_buttons'][] = $action_button;
            $column_found = true;
            break;
        }
    }

    if (!$column_found) {
        throw new Exception('Column not found');
    }

    $updated_preferences = array_merge($preferences, [
        'structure' => $structure,
        'updated_at' => date('Y-m-d H:i:s')
    ]);

    save_column_preferences($table_name, $updated_preferences, $p['data_source'] ?? null);

    return data_table::regenerate_with_preferences($p);
}

/**
 * Remove action button from a column
 */
function remove_action_button($p) {
    $table_name = $p['table_name'] ?? '';
    $column_id = $p['column_id'] ?? '';
    $action_id = $p['action_id'] ?? '';

    if (empty($column_id)) {
        throw new Exception('Column ID is required');
    }

    if (empty($action_id)) {
        throw new Exception('Action ID is required');
    }

    $preferences = get_column_preferences($table_name);
    $structure = $preferences['structure'] ?? [];

    // Find the column and remove the action button
    $column_found = false;
    foreach ($structure as &$column) {
        if ($column['id'] === $column_id) {
            if (isset($column['action_buttons'])) {
                $column['action_buttons'] = array_filter($column['action_buttons'], function($action) use ($action_id) {
                    return $action['id'] !== $action_id;
                });
                $column['action_buttons'] = array_values($column['action_buttons']); // Re-index
            }
            $column_found = true;
            break;
        }
    }

    if (!$column_found) {
        throw new Exception('Column not found');
    }

    $updated_preferences = array_merge($preferences, [
        'structure' => $structure,
        'updated_at' => date('Y-m-d H:i:s')
    ]);

    save_column_preferences($table_name, $updated_preferences, $p['data_source'] ?? null);

    return data_table::regenerate_with_preferences($p);
}

/**
 * Move action button between columns
 */
function move_action_button($p) {
    $table_name = $p['table_name'] ?? '';
    $action_id = $p['action_id'] ?? '';
    $source_column_id = $p['source_column_id'] ?? '';
    $target_column_id = $p['target_column_id'] ?? '';

    if (empty($action_id) || empty($source_column_id) || empty($target_column_id)) {
        throw new Exception('Action ID, source column ID, and target column ID are required');
    }

    if ($source_column_id === $target_column_id) {
        return data_table::regenerate_with_preferences($p); // No change needed
    }

    $preferences = get_column_preferences($table_name);
    $structure = $preferences['structure'] ?? [];

    $action_button = null;
    $source_found = false;
    $target_found = false;

    // Find and remove action button from source column
    foreach ($structure as &$column) {
        if ($column['id'] === $source_column_id) {
            if (isset($column['action_buttons'])) {
                foreach ($column['action_buttons'] as $index => $action) {
                    if ($action['id'] === $action_id) {
                        $action_button = $action;
                        unset($column['action_buttons'][$index]);
                        $column['action_buttons'] = array_values($column['action_buttons']); // Re-index
                        $source_found = true;
                        break;
                    }
                }
            }
            break;
        }
    }

    if (!$source_found || !$action_button) {
        throw new Exception('Action button not found in source column');
    }

    // Add action button to target column
    foreach ($structure as &$column) {
        if ($column['id'] === $target_column_id) {
            if (!isset($column['action_buttons'])) {
                $column['action_buttons'] = [];
            }
            $column['action_buttons'][] = $action_button;
            $target_found = true;
            break;
        }
    }

    if (!$target_found) {
        throw new Exception('Target column not found');
    }

    $updated_preferences = array_merge($preferences, [
        'structure' => $structure,
        'updated_at' => date('Y-m-d H:i:s')
    ]);

    save_column_preferences($table_name, $updated_preferences, $p['data_source'] ?? null);

    return data_table::regenerate_with_preferences($p);
}

/**
 * Show add action form
 */
function show_add_action_form($p) {
    $table_name = $p['table_name'] ?? '';
    $column_id = $p['column_id'] ?? '';
    $loop_index = $p['loop_index'] ?? 0;

    if (empty($column_id)) {
        throw new Exception('Column ID is required');
    }

    // Get available field list - reuse logic from main component
    $available_field_list = [];

    // Get data source information to determine available fields
    $data_source_id = $p['data_source'] ?? null;
    if ($data_source_id) {
        $data_source = \system\data_source_manager::get_data_source($data_source_id);
        if ($data_source) {
            $items = \system\data_source_manager::get_data($data_source_id);
            if (!empty($items)) {
                $first_item = reset($items);
                $available_field_list = array_keys($first_item);
            }
        }
    } else {
        // For hardcoded data sources, we need to get the callback and extract fields
        $callback = $p['callback'] ?? '';
        if (!empty($callback) && function_exists($callback)) {
            $items = call_user_func($callback);
            if (!empty($items)) {
                $first_item = reset($items);
                $available_field_list = array_keys($first_item);
            }
        }
    }

    return edge::render('data-table-add-action-form', [
        'table_name' => $table_name,
        'callback' => $p['callback'] ?? '',
        'column_id' => $column_id,
        'loop_index' => $loop_index,
        'available_field_list' => $available_field_list
    ]);
}

/**
 * Cancel add action form (returns empty content)
 */
function cancel_add_action() {
    return '';
}
