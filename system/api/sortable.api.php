<?php
/**
 * Universal Sortable API Endpoints
 * 
 * This file provides example endpoints for handling sortable operations
 * using the new simplified sortable system with HTMX inline attributes.
 */

/**
 * Reorder basic items in a list
 */
function reorder_items($p) {
    $item_ids = $p['item_ids'] ?? [];
    
    if (empty($item_ids)) {
        return '<div class="text-red-600">No items to reorder</div>';
    }
    
    // Here you would typically update your database
    // For example:
    // foreach ($item_ids as $index => $item_id) {
    //     update_item_order($item_id, $index + 1);
    // }
    
    // Log the reorder for demonstration
    tcs_log("Items reordered: " . implode(', ', $item_ids), 'sortable');
    
    return '<div class="text-green-600">✓ Items reordered successfully! New order: ' . implode(', ', $item_ids) . '</div>';
}

/**
 * Reorder tasks with additional data
 */
function reorder_tasks($p) {
    $task_ids = $p['task_ids'] ?? [];
    
    if (empty($task_ids)) {
        return '<div class="text-red-600">No tasks to reorder</div>';
    }
    
    // Example database update
    // foreach ($task_ids as $index => $task_id) {
    //     $db->update('tasks', [
    //         'sort_order' => $index + 1,
    //         'updated_at' => date('Y-m-d H:i:s')
    //     ], ['id' => $task_id]);
    // }
    
    tcs_log("Tasks reordered: " . implode(', ', $task_ids), 'sortable');
    
    return '<div class="text-blue-600">✓ Tasks reordered successfully! New order: ' . implode(', ', $task_ids) . '</div>';
}

/**
 * Handle moving items between different lists/groups
 */
function move_between_lists($p) {
    $task_ids = $p['task_ids'] ?? [];
    $list_id = $p['list_id'] ?? '';
    
    if (empty($task_ids) || empty($list_id)) {
        return '<div class="text-red-600">Missing required data for list move</div>';
    }
    
    // Example: Update database to reflect new list assignment and order
    // foreach ($task_ids as $index => $task_id) {
    //     $db->update('tasks', [
    //         'list_id' => $list_id,
    //         'sort_order' => $index + 1,
    //         'updated_at' => date('Y-m-d H:i:s')
    //     ], ['id' => $task_id]);
    // }
    
    tcs_log("Tasks moved to list '$list_id': " . implode(', ', $task_ids), 'sortable');
    
    return '<div class="text-purple-600">✓ Tasks moved to ' . ucfirst($list_id) . ' list! Order: ' . implode(', ', $task_ids) . '</div>';
}

/**
 * Generic sortable handler that can work with any sortable data
 */
function generic_reorder($p) {
    // Extract common parameters
    $table_name = $p['table_name'] ?? '';
    $id_field = $p['id_field'] ?? 'id';
    $order_field = $p['order_field'] ?? 'sort_order';
    $item_ids = [];
    
    // Look for various possible ID field names
    $possible_id_fields = ['item_ids', 'task_ids', 'column_ids', 'field_ids', 'ids'];
    
    foreach ($possible_id_fields as $field) {
        if (!empty($p[$field])) {
            $item_ids = $p[$field];
            break;
        }
    }
    
    if (empty($table_name) || empty($item_ids)) {
        return '<div class="text-red-600">Missing required parameters: table_name and item IDs</div>';
    }
    
    try {
        // Use the database class to update sort orders
        $db = new database();
        
        foreach ($item_ids as $index => $item_id) {
            $db->update($table_name, [
                $order_field => $index + 1,
                'updated_at' => date('Y-m-d H:i:s')
            ], [$id_field => $item_id]);
        }
        
        tcs_log("Generic reorder completed for table '$table_name': " . implode(', ', $item_ids), 'sortable');
        
        return '<div class="text-green-600">✓ Items reordered successfully in ' . $table_name . '</div>';
        
    } catch (Exception $e) {
        tcs_log("Error in generic_reorder: " . $e->getMessage(), 'sortable_error');
        return '<div class="text-red-600">Error updating sort order: ' . $e->getMessage() . '</div>';
    }
}

/**
 * Advanced sortable handler with custom callback support
 */
function advanced_reorder($p) {
    $callback = $p['callback'] ?? '';
    $item_ids = $p['item_ids'] ?? [];
    
    if (empty($item_ids)) {
        return '<div class="text-red-600">No items to reorder</div>';
    }
    
    // If a callback is specified, try to call it
    if (!empty($callback) && function_exists($callback)) {
        try {
            $result = call_user_func($callback, $p);
            return $result;
        } catch (Exception $e) {
            tcs_log("Error calling callback '$callback': " . $e->getMessage(), 'sortable_error');
            return '<div class="text-red-600">Error in callback: ' . $e->getMessage() . '</div>';
        }
    }
    
    // Default behavior
    return generic_reorder($p);
}

/**
 * Example of integrating with existing column preferences system
 */
function reorder_columns_simple($p) {
    // This is a simplified version that works with the new sortable system
    // while maintaining compatibility with existing column preferences
    
    $table_name = $p['table_name'] ?? '';
    $column_ids = $p['column_ids'] ?? [];
    
    if (empty($table_name) || empty($column_ids)) {
        return '<div class="text-red-600">Missing table name or column IDs</div>';
    }
    
    try {
        // Call the existing column preferences function
        $p['column_order'] = $column_ids; // Ensure it's an array, not JSON
        
        // Use existing column preferences API
        require_once 'data_table/column_preferences.api.php';
        return reorder_columns($p);
        
    } catch (Exception $e) {
        tcs_log("Error in reorder_columns_simple: " . $e->getMessage(), 'sortable_error');
        return '<div class="text-red-600">Error reordering columns: ' . $e->getMessage() . '</div>';
    }
}

/**
 * Helper function to extract item IDs from sortable container
 * This can be used in JavaScript to prepare data for submission
 */
function get_sortable_order($container_selector, $item_selector = '[data-item-id]', $id_attribute = 'data-item-id') {
    // This is a PHP function that generates JavaScript code
    // to be used in templates for extracting sort order
    
    return "
    function getSortableOrder(containerSelector = '$container_selector', itemSelector = '$item_selector', idAttribute = '$id_attribute') {
        const container = document.querySelector(containerSelector);
        if (!container) return [];
        
        const items = container.querySelectorAll(itemSelector);
        return Array.from(items).map(item => item.getAttribute(idAttribute));
    }
    ";
}

/**
 * Utility function to validate sortable request
 */
function validate_sortable_request($p, $required_fields = ['item_ids']) {
    $errors = [];
    
    foreach ($required_fields as $field) {
        if (empty($p[$field])) {
            $errors[] = "Missing required field: $field";
        }
    }
    
    // Check if item_ids is actually an array
    if (isset($p['item_ids']) && !is_array($p['item_ids'])) {
        $errors[] = "item_ids must be an array";
    }
    
    return $errors;
}

/**
 * Example of handling nested sortable items (like navigation trees)
 */
function reorder_nested_items($p) {
    $item_ids = $p['item_ids'] ?? [];
    $parent_id = $p['parent_id'] ?? null;
    $table_name = $p['table_name'] ?? 'navigation';
    
    if (empty($item_ids)) {
        return '<div class="text-red-600">No items to reorder</div>';
    }
    
    try {
        $db = new database();
        
        foreach ($item_ids as $index => $item_id) {
            $update_data = [
                'sort_order' => $index + 1,
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            // If parent_id is specified, update it too (for moving between levels)
            if ($parent_id !== null) {
                $update_data['parent_id'] = $parent_id;
            }
            
            $db->update($table_name, $update_data, ['id' => $item_id]);
        }
        
        tcs_log("Nested items reordered in '$table_name': " . implode(', ', $item_ids), 'sortable');
        
        return '<div class="text-green-600">✓ Navigation items reordered successfully</div>';
        
    } catch (Exception $e) {
        tcs_log("Error in reorder_nested_items: " . $e->getMessage(), 'sortable_error');
        return '<div class="text-red-600">Error updating navigation order: ' . $e->getMessage() . '</div>';
    }
}
